<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-task-sfa</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>fs-crm-task-sfa-web</module>
        <module>fs-crm-task-sfa-executor</module>
        <module>fs-crm-task-sfa-common</module>
    </modules>

    <properties>
        <paas.app.metadata.version>9.6.0-SNAPSHOT</paas.app.metadata.version>
        <task.sfa.reversion>9.7.0-SNAPSHOT</task.sfa.reversion>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${paas.app.metadata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-rest-client-core</artifactId>
                <version>6.6.5-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-sandbox-api</artifactId>
            <version>2.0.4-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.7.0.1746</version>
            </plugin>
        </plugins>
    </build>

</project>
