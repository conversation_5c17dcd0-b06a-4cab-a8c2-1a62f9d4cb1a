package com.facishare.crm.task.sfa.loyalty.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyEvaluationService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberChangeRecordsService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.loyalty.task.LoyaltyMqProducer;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoyaltyPointsDetailClearService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyMqProducer mqProducer;
    @Resource
    LoyaltyMemberChangeRecordsService loyaltyMemberChangeRecordsService;
    @Resource
    LoyaltyEvaluationService loyaltyEvaluationService;

    /**
     * 每晚执行,用于删除过期积分明细,并返回需要重新计算的会员id列表
     */
    @Transactional
    public Set<String> clearPointsDetailForExpired(TaskLoyalty.CoreTaskInfo coreTaskInfo) {
        log.info("会员积分明细状态检测-执行参数.param:[{}]", JSON.toJSONString(coreTaskInfo));
        String tenantId = coreTaskInfo.getTenantId();
        List<IObjectData> dataList = findLoyaltyPointsDetail(coreTaskInfo);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("会员积分明细状态检测-完成");
            return new HashSet<>();
        }
        List<String> pointsTypeIdList = dataList.stream()
                .map(pointsDetail -> pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_TYPE_ID, String.class))
                .filter(id -> !StringUtils.isEmpty(id))
                .collect(Collectors.toList());
        List<IObjectData> pointsTypeList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, pointsTypeIdList, LoyaltyConstants.LoyaltyPointType.API_NAME);
        Map<String, IObjectData> pointsTypeMap = new HashMap<>();
        for (IObjectData pointsType : pointsTypeList) {
            pointsTypeMap.put(pointsType.getId(), pointsType);
        }
        List<IObjectData> memberChangeRecordList = new ArrayList<>();
        for (IObjectData pointsDetail : dataList) {
            IObjectData pointsType = pointsTypeMap.get(pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_TYPE_ID, String.class));
            String changeType = "";
            if (pointsType != null) {
                if ("true".equals(pointsType.get("is_qualifying", String.class))) {
                    changeType = "LEVEL_POINTS_EXPIRED";
                } else {
                    changeType = "CONSUMER_POINTS_EXPIRED";
                }
            }
            IObjectData memberChangeRecord = toMemberChangeRecord(pointsDetail, changeType);
            memberChangeRecordList.add(memberChangeRecord);
        }
        loyaltyMemberChangeRecordsService.saveMemberChangeRecord(tenantId, memberChangeRecordList);
        serviceFacade.bulkDeleteDirect(dataList, User.systemUser(tenantId));
        return dataList.stream().map(e -> e.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class)).collect(Collectors.toSet());
    }

    public List<IObjectData> findLoyaltyPointsDetail(TaskLoyalty.CoreTaskInfo coreTaskInfo) {
        String tenantId = coreTaskInfo.getTenantId();
        long currentTime = System.currentTimeMillis();
        List<IFilter> filtersExpired = new ArrayList<>();
        if (!StringUtils.isEmpty(coreTaskInfo.getPointsDetailExpiredOffset())) {
            log.info("会员积分明细状态检测-开始.tenantId:[{}],param:[{}]", tenantId, JSON.toJSONString(coreTaskInfo));
            SearchUtil.fillFilterGT(filtersExpired, IObjectData.ID, coreTaskInfo.getPointsDetailExpiredOffset());
        }
        filtersExpired.addAll(LoyaltyPointsDetailService.buildFilterByPointsStatus(LoyaltyConstants.LoyaltyPointsDetail.PointsStatus.Expired, currentTime));
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.getFilters().addAll(filtersExpired);
        query.setLimit(LoyaltyThreshold.getCommonPageSize());
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return new ArrayList<>();
        }
        List<IObjectData> dataList = queryResult.getData();
        IObjectData lastData = dataList.get(dataList.size() - 1);
        String offset = lastData.getId();
        coreTaskInfo.setPointsDetailExpiredOffset(offset);
        return dataList;
    }

    /**
     * 评定日异步清空定级积分
     */
    @Transactional
    public void clearPointsDetailForEvaluationDate(Loyalty.ClearPointsDetail clearPointsDetail) {
        log.info("会员评定日清空积分-执行参数.param:[{}]", JSON.toJSONString(clearPointsDetail));
        String tenantId = clearPointsDetail.getTenantId();
        String memberId = clearPointsDetail.getMemberId();
        Long lastEvaluationDate = clearPointsDetail.getLastEvaluationDate();
        List<IObjectData> dataList = findLoyaltyPointsDetail(clearPointsDetail);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("会员评定日清空积分-完成");
            return;
        }
        List<IObjectData> memberChangeRecordList = new ArrayList<>();
        for (IObjectData pointsDetail : dataList) {
            IObjectData memberChangeRecord = toMemberChangeRecord(pointsDetail, "LEVEL_POINTS_CLEAR");
            memberChangeRecordList.add(memberChangeRecord);
        }
        loyaltyMemberChangeRecordsService.saveMemberChangeRecord(tenantId, memberChangeRecordList);
        serviceFacade.bulkDeleteDirect(dataList, User.systemUser(tenantId));
        loyaltyEvaluationService.setLastEvaluationDate(tenantId, memberId, lastEvaluationDate);
        mqProducer.asyncClearPointsDetail(clearPointsDetail);
    }

    /**
     * 根据偏移量查询积分明细列表
     * 查询结果中偏移量会回填到入参中
     */
    public List<IObjectData> findLoyaltyPointsDetail(Loyalty.ClearPointsDetail clearPointsDetail) {
        String tenantId = clearPointsDetail.getTenantId();
        String memberId = clearPointsDetail.getMemberId();
        Long lastEvaluationDate = clearPointsDetail.getLastEvaluationDate();
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, memberId);
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Operator.EQ, true);
        iFilter.setIsMasterField(true);
        filters.add(iFilter);
        SearchUtil.fillFilterLT(filters, IObjectData.CREATE_TIME, lastEvaluationDate);
        if (!StringUtils.isEmpty(clearPointsDetail.getOffset())) {
            log.info("会员评定日清空积分-开始.tenantId:[{}],param:[{}]", tenantId, JSON.toJSONString(clearPointsDetail));
            SearchUtil.fillFilterGT(filters, IObjectData.ID, clearPointsDetail.getOffset());
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(LoyaltyThreshold.getCommonPageSize());
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return new ArrayList<>();
        }
        List<IObjectData> dataList = queryResult.getData();
        IObjectData lastData = dataList.get(dataList.size() - 1);
        String lastDataId = lastData.getId();
        clearPointsDetail.setOffset(lastDataId);
        return dataList;
    }

    /**
     * 积分明细对象转换成会员变动记录
     */
    public IObjectData toMemberChangeRecord(IObjectData pointsDetail, String changeType) {
        ObjectDataDocument memberChangeRecord = new ObjectDataDocument();
        memberChangeRecord.put(IObjectData.TENANT_ID, pointsDetail.getTenantId());
        memberChangeRecord.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, pointsDetail.getId());
        String programId = pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.PROGRAM_ID, String.class);
        memberChangeRecord.put(LoyaltyConstants.LoyaltyMemberChangeRecords.PROGRAM_ID, programId);
        String memberId = pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class);
        memberChangeRecord.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, memberId);
        String value = pointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, String.class);
        memberChangeRecord.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, value);
        memberChangeRecord.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, changeType);
        return memberChangeRecord.toObjectData();
    }
}
