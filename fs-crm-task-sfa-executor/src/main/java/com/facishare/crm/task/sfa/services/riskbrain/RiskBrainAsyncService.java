package com.facishare.crm.task.sfa.services.riskbrain;

import cn.com.antcloud.api.AntFinTechApiClient;
import cn.com.antcloud.api.riskplus.v1_0.response.QueryGeneralResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.riskbrain.model.RiskBrainModel;
import com.facishare.crm.sfa.lto.riskbrain.service.RiskBrainCommonService;
import com.facishare.crm.sfa.lto.utils.ActionContextUtil;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.sfa.lto.utils.RSARiskBrainTool;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.common.util.I18NKeyUtil;
import com.facishare.crm.task.sfa.procurement.service.NomonTask;
import com.facishare.crm.task.sfa.rest.CRMMetaDataService;
import com.facishare.crm.task.sfa.util.DateUtils;
import com.facishare.crm.task.sfa.util.constant.AllocateConstantsUtils;
import com.facishare.crm.task.sfa.util.constant.RiskBrainConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.common.constants.AccountRiskConstants.*;

/**
 * <AUTHOR> lik
 * @date : 2023/3/7 10:47
 */
@Component
@Slf4j
public class RiskBrainAsyncService {

    @Autowired
    private RiskBrainCommonService riskBrainCommonService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    private CRMMetaDataService crmMetaDataService;
    @Autowired
    private NomonTask nomonTask;
    @Autowired
    private SfaTaskRateLimiterService limiterService;

    public void syncRiskInfo(String tenantId) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        TraceContext.get().setTraceId(traceId);
        log.info("同步风险信息开始[{}]", tenantId);
        syncRiskInfoByTenantId(tenantId);
        syncCreditLimitByTenantIdAndUcCode(tenantId);
        log.info("同步风险信息完成[{}]", tenantId);
    }

    public void syncCreditLimitByTenantIdAndUcCode(String tenantId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
        SearchUtil.fillFilterEq(filters, ENABLE_RISK_PORTRAIT, true);
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.resetFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), SystemConstants.AccountApiName, searchTemplateQuery);
        if (queryResult.getData() == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return;
        }
        List<IObjectData> updateData = new ArrayList<>();
        for (IObjectData objectData : queryResult.getData()) {
            String ucCode = objectData.get(CREDIT_CODE, String.class);
            if (StringUtils.isEmpty(ucCode))
                continue;
            RiskBrainConstants.AccountCreditLimit creditLimit = findAccountCreditLimit(tenantId, ucCode);
            if (creditLimit != null && !CollectionUtils.isEmpty(creditLimit.getRecommendedCreditAmount())) {
                objectData.set(CREDIT_LIMIT_LOWER, String.format("%.2f", creditLimit.getRecommendedCreditAmount().get(0)));
                if (creditLimit.getRecommendedCreditAmount().size() > 1) {
                    objectData.set(CREDIT_LIMIT_UPPER, String.format("%.2f", creditLimit.getRecommendedCreditAmount().get(1)));
                }
                updateData.add(objectData);
            }
        }
        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateData, Lists.newArrayList(CREDIT_LIMIT_LOWER, CREDIT_LIMIT_UPPER));
        updateData.forEach(data -> infraServiceFacade.startWorkFlow(data.getId(),
                data.getDescribeApiName(), WorkflowProducer.TRIGGER_UPDATE, User.systemUser(data.getTenantId()), ObjectDataExt.toMap(data)));
    }

    /**
     * 查询授信推荐额度
     *
     * @param tenantId
     * @param ucCode
     * @return
     */
    private RiskBrainConstants.AccountCreditLimit findAccountCreditLimit(String tenantId, String ucCode) {
        RiskBrainConstants.AccountCreditLimit res = new RiskBrainConstants.AccountCreditLimit();
        JSONObject param = new JSONObject();
        param.put("keyword", ucCode);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.customer.recommended.credit", param);
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            return data.getObject("queryResult", RiskBrainConstants.AccountCreditLimit.class);
        }
        return res;
    }

    public void syncRiskInfoByTenantId(String tenantId) {
        int size = 50;
        for (int page = 0; true; page++) {
            List<RiskBrainConstants.AccountRiskInfo> riskInfoList = findRiskInfo(tenantId, size, page * size);
            if (CollectionUtils.isEmpty(riskInfoList)) {
                break;
            }
            Map<String, RiskBrainConstants.AccountRiskInfo> riskInfoMap = riskInfoList.stream()
                    .collect(Collectors.toMap(RiskBrainConstants.AccountRiskInfo::getUcCode, Function.identity()));
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, tenantId);
            SearchUtil.fillFilterIn(filters, CREDIT_CODE, Lists.newArrayList(riskInfoMap.keySet()));
            SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
            searchTemplateQuery.resetFilters(filters);
            searchTemplateQuery.setLimit(size);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), SystemConstants.AccountApiName, searchTemplateQuery);
            List<IObjectData> updateList = new ArrayList<>();
            for (IObjectData iObjectData : queryResult.getData()) {
                RiskBrainConstants.AccountRiskInfo riskInfo = riskInfoMap.get(iObjectData.get(CREDIT_CODE, String.class));
                iObjectData.set(THIRD_PARTY_RISK_LEVEL, riskInfo.getRuleResultAbstract());
                if (riskInfo.getDiyCompanyScore() != null) {
                    iObjectData.set(THIRD_PARTY_RISK_SCORES, riskInfo.getDiyCompanyScore());
                }
                iObjectData.set(FORMULA_CREDIT_LIMIT, riskInfo.getCreditAmount());

                iObjectData.set(RISK_SCORES_MODEL_NAME, riskInfo.getRuleName());
                iObjectData.set(SELF_SERVICE_SCORES_RULE_NAME, riskInfo.getSolutionName());
                iObjectData.set(CREDIT_RULE_NAME, riskInfo.getCreditRuleName());
                updateList.add(iObjectData);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateList, Lists.newArrayList(
                        THIRD_PARTY_RISK_LEVEL, THIRD_PARTY_RISK_SCORES, FORMULA_CREDIT_LIMIT,
                        RISK_SCORES_MODEL_NAME, SELF_SERVICE_SCORES_RULE_NAME, CREDIT_RULE_NAME));
                log.warn("风险大脑定时更新[{}][{}]", tenantId, JSON.toJSONString(updateList));
                updateList.forEach(data -> infraServiceFacade.startWorkFlow(data.getId(),
                        data.getDescribeApiName(), WorkflowProducer.TRIGGER_UPDATE, User.systemUser(data.getTenantId()), ObjectDataExt.toMap(data)));
            }
        }
    }

    /**
     * 分页查询风险信息
     *
     * @param tenantId
     * @param limit
     * @param offset
     * @return
     */
    private List<RiskBrainConstants.AccountRiskInfo> findRiskInfo(String tenantId, int limit, int offset) {
        JSONObject param = new JSONObject();
        param.put("customerName", tenantId);
        param.put("limit", limit);
        param.put("offset", offset);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.customer.inquiry.list", param);
        if (response.isSuccess()) {
            JSONObject data = JSON.parseObject(response.getData());
            JSONObject result = data.getJSONObject("queryResult");
            return JSON.parseArray(result.getString("items"), RiskBrainConstants.AccountRiskInfo.class);
        }
        return Lists.newArrayList();
    }

    public void remindHandle(String tenantId) {
        try {
            RiskBrainModel.Arg paramArg = riskBrainCommonService.getRiskBrainUserInfo(tenantId);
            // 初始化Client
            AntFinTechApiClient antFinTechApiClient = riskBrainCommonService.getAntFinTechApiClient();
            int index = 1;
            for (int loopTimes = 0; true; loopTimes++) {
                if (loopTimes >= 1000) {
                    log.error("蚂蚁风险查询事件循环超长");
                    break;
                }
                RiskBrainConstants.ResultData responseCompany = getInvolvedAccount(antFinTechApiClient, index, paramArg);
                if (ObjectUtils.isEmpty(responseCompany) || CollectionUtils.isEmpty(responseCompany.getItemList())) {
                    break;
                }
                List<String> unCodeList = responseCompany.getItemList().stream().map(RiskBrainConstants.ResultItem::getUcCode).collect(Collectors.toList());
                User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                //获取企业信息
                List<IObjectData> objectDataList = getAccountInfoByUniformSocialCreditCode(user, unCodeList);
                Map<String, List<IObjectData>> objectDataMap = objectDataList.stream().collect(Collectors.groupingBy(data -> data.get("uniform_social_credit_code").toString()));

                unCodeList.forEach(unCode -> {
                    limiterService.getRiskBrainLimiter().acquire();
                    String data = getEventByAccount(unCode, antFinTechApiClient);
                    if (ObjectUtils.isEmpty(data)) {
                        return;
                    }
                    if (!objectDataMap.containsKey(unCode)) {
                        log.warn("RiskBrainAsyncService handle error tenantId:{},unCode:{}", tenantId, unCode);
                        return;
                    }
                    List<IObjectData> objectDatasList = objectDataMap.get(unCode);
                    objectDatasList.forEach(objectData -> {
                        if (CollectionUtils.isEmpty(objectData.getOwner())) {
                            return;
                        }
                        objectData.getOwner().forEach(ownerId -> sendCRMMsgToOnwer(user, objectData.getOwner().get(0), objectData.getName(), objectData.getId()));
                    });
                });
                if (index >= responseCompany.getTotalPages()) {
                    break;
                }
                index++;
            }
        } catch (Exception e) {
            log.error("RiskBrainAsyncService handle error tenantId:{},e:", tenantId, e);
        }
    }


    public List<IObjectData> getAccountInfoByUniformSocialCreditCode(User user, List<String> uniformSocialCreditCode) {
        IActionContext actionContext = ActionContextUtil.createSearchActionContext(user, true, false);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.TENANT_ID, user.getTenantId());
        SearchUtil.fillFilterIn(filters, "uniform_social_credit_code", uniformSocialCreditCode);
        SearchUtil.fillFilterEq(filters, DBRecord.IS_DELETED, 0);
        searchTemplateQuery.resetFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchTemplateQueryWithFields(actionContext, SystemConstants.AccountApiName, searchTemplateQuery,
                Lists.newArrayList("_id", "name", "owner", "uniform_social_credit_code"));
        return queryResult.getData();
    }

    public void sendCRMMsgToOnwer(User user, String sender, String name, String id) {
        String title = I18N.text(I18NKeyUtil.SFA_RISK_BRAIN_MONITORING_REMINDER, I18N.text("AccountObj.attribute.self.display_name"));
        String content = I18N.text(I18NKeyUtil.SFA_RISK_BRAIN_MONITORING_REMINDER_CONTENT, name);
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(User.SUPPER_ADMIN_USER_ID)
                .remindRecordType(210)
                .title(title)
                .content(content)
                .dataId(id)
                .content2Id(sender)
                .receiverIds(Sets.newHashSet(Integer.valueOf(sender)))
                .objectApiName(SystemConstants.AccountApiName)
                .build();
        crmNotificationService.sendCRMNotification(user, crmNotification);
        //发送新crm通知
        Map<String, String> urlParameter = Maps.newHashMap();
        urlParameter.put(AllocateConstantsUtils.OBJECT_API_NAME, SystemConstants.AccountApiName);
        urlParameter.put(AllocateConstantsUtils.OBJECT_ID, id);
        //发送新crm通知
        CRMRecordUtil.sendNewCRMRecord(crmNotificationService, user, 210, Lists.newArrayList(Integer.valueOf(sender)),
                user.getUserId(), title, content, I18NKeyUtil.SFA_RISK_BRAIN_MONITORING_REMINDER,
                Lists.newArrayList(String.format("#I18N#%s", "AccountObj.attribute.self.display_name")), I18NKeyUtil.SFA_RISK_BRAIN_MONITORING_REMINDER_CONTENT, Lists.newArrayList(name),
                urlParameter);
    }

    /**
     * 获取关注的企业
     */
    public RiskBrainConstants.ResultData getInvolvedAccount(AntFinTechApiClient antFinTechApiClient, int index, RiskBrainModel.Arg paramArg) {
        try {
            JSONObject request = new JSONObject();
            request.put("pageNo", index);
            request.put("queryTenantCode", paramArg.getTenantCode());
            QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral(antFinTechApiClient, "irap.company.monitor.list", request);
            if (!"OK".equals(responseCompany.getResultCode())) {
                log.error("RiskBrainAsyncService getInvolvedAccount error msg:{}", responseCompany.getResultMsg());
                return null;
            }
            if (ObjectUtils.isEmpty(responseCompany.getData())) {
                return null;
            }
            RiskBrainConstants.ResultResp result = JSONObject.parseObject(responseCompany.getData(), RiskBrainConstants.ResultResp.class);
            return result.getQueryResult();
        } catch (Exception e) {
            log.error("RiskBrainAsyncService,getEventByAccount error e ", e);
        }
        return null;
    }

    /**
     * 根据企业获取企业变更事件
     */
    public String getEventByAccount(String unCode, AntFinTechApiClient antFinTechApiClient) {
        try {
            JSONObject request = new JSONObject();

            request.put("keyword", unCode);
            request.put("offset", "0");
            request.put("limit", "1");
            request.put("begin_time", DateUtils.getTimeByTimeStamp(riskBrainCommonService.getQuerySecond()));
            request.put("end_time", DateUtils.getCurrentTimeToStr());

            QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral(antFinTechApiClient, "rbb.company.risk.monitor", request);
            if (ObjectUtils.isEmpty(responseCompany) || !"OK".equals(responseCompany.getResultCode())) {
                log.error("RiskBrainAsyncService getEventByAccount error msg:{}", responseCompany.getResultMsg());
                return "";
            }
            JSONObject object = JSON.parseObject(responseCompany.getData());
            if (!object.containsKey("queryResult") || ObjectUtils.isEmpty(object.get("queryResult"))) {
                return "";
            }

            JSONObject queryResult = object.getJSONObject("queryResult");
            if (!queryResult.containsKey("hits") || ObjectUtils.isEmpty(queryResult.get("hits"))) {
                return "";
            }
            JSONObject hits = queryResult.getJSONObject("hits");
            if (!hits.containsKey("hits") || ObjectUtils.isEmpty(hits.get("hits"))) {
                return "";
            }
            return responseCompany.getData();
        } catch (Exception e) {
            log.error("RiskBrainAsyncService,getEventByAccount error e ", e);
        }
        return null;
    }

    public void initRiskBrainAccount(RiskBrian.OrderParam param) {
        String tenantId = param.getTenantId();
        RiskBrainModel.Arg account = riskBrainCommonService.getRiskBrainUser(tenantId);
        if (account == null || StringUtils.isEmpty(account.getUserName())) {
            //创建账号
            createAccount(param);
        } else {
            //续期
            orderRenewal(param);
        }
        createAllTask(tenantId);
        crmMetaDataService.refreshRiskBrainButton(tenantId);
    }

    public void orderRenewal(RiskBrian.OrderParam param) {
        String tenantId = param.getTenantId();
        Long startTime = param.getStartTime();
        Long expiredTime = param.getExpiredTime();
        Boolean official = param.getOfficial();
        JSONObject request = new JSONObject();
        request.put("clientName", tenantId);
        request.put("orderId", getOrderId(tenantId));
        request.put("expiryDate", DateUtil.getDataStrByTimeStamp(expiredTime));
        request.put("effectiveTime", DateUtil.getDataTimeStrByTimeStamp(startTime));
        if (official) {
            request.put("isChangeToOfficial", 1);
        } else {
            request.put("isChangeToOfficial", 0);
        }
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.account.expiry.extend", request);
        if (!response.isSuccess()) {
            throw new ValidateException("蚂蚁续期失败:" + response.getData());
        }
    }

    public void placeOrder(String tenantId, long count) {
        JSONObject request = new JSONObject();
        request.put("clientName", tenantId);
        request.put("applyProductQuota", count);
        request.put("orderId", getOrderId(tenantId));
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.account.quota.apply", request);
        if (!response.isSuccess()) {
            throw new ValidateException("蚂蚁下单失败:" + response.getData());
        }
    }

    public String getOrderId(String tenantId) {
        JSONObject request = new JSONObject();
        request.put("clientName", tenantId);
        QueryGeneralResponse response = riskBrainCommonService.queryGeneral("irap.account.order.query", request);
        if (!response.isSuccess()) {
            throw new ValidateException("蚂蚁查询订单失败:" + response.getData());
        }
        JSONObject responseData = JSON.parseObject(response.getData());
        JSONObject queryResult = responseData.getJSONObject("queryResult");
        String orderId = queryResult.getString("orderId");
        if (!StringUtils.isEmpty(orderId)) {
            return orderId;
        }
        JSONArray orderList = queryResult.getJSONArray("orderList");
        if (!CollectionUtils.isEmpty(orderList)) {
            JSONObject order = orderList.getJSONObject(0);
            return order.getString("order_id");
        }
        throw new ValidateException("蚂蚁未找到订单");
    }

    /**
     * 创建账号
     */
    private void createAccount(RiskBrian.OrderParam param) {
        String tenantId = param.getTenantId();
        String orderNumber = param.getOrderId();
        Long createTime = param.getCreateTime();
        Long startTime = param.getStartTime();
        Long expiredTime = param.getExpiredTime();
        Boolean official = param.getOfficial();
        AntFinTechApiClient antFinTechApiClient = riskBrainCommonService.getAntFinTechApiClient();
        JSONObject request = new JSONObject();
        request.put("clientName", tenantId);
        request.put("orderId", orderNumber);
        request.put("orderTime", DateUtil.getDataTimeStrByTimeStamp(createTime));
        request.put("expiryDate", DateUtil.getDataStrByTimeStamp(expiredTime));
        request.put("effectiveTime", DateUtil.getDataTimeStrByTimeStamp(startTime));
        request.put("productType", 101);
        if (Boolean.TRUE.equals(official)) {
            request.put("tenantType", "CUSTOMER_OFFICIAL");
        } else {
            request.put("tenantType", "CUSTOMER_TEST");
        }
        QueryGeneralResponse responseCompany = riskBrainCommonService.queryGeneral(antFinTechApiClient, "irap.account.create.apply", request);
        if (!responseCompany.isSuccess() || ObjectUtils.isEmpty(responseCompany) || ObjectUtils.isEmpty(responseCompany.getData())) {
            throw new ValidateException("蚂蚁创建账号失败:" + responseCompany.getData());
        }
        RiskBrainModel.AccountCreateApplyResult accountCreateApplyResult = JSONObject.parseObject(responseCompany.getData(), RiskBrainModel.AccountCreateApplyResult.class);
        RiskBrainModel.AccountCreateApplyQueryResult accountCreateApplyQueryResult = accountCreateApplyResult.getQueryResult();
        try {
            String password = RSARiskBrainTool.decryptStr(accountCreateApplyQueryResult.getEncryptedPassword(), riskBrainCommonService.getPrivateKey());
            RiskBrainModel.Arg arg = new RiskBrainModel.Arg();
            arg.setTenantId(tenantId);
            arg.setTenantCode(accountCreateApplyQueryResult.getTenantCode());
            arg.setUserName(accountCreateApplyQueryResult.getAccountName());
            arg.setPassword(password);
            serviceFacade.upsertTenantConfig(User.systemUser(tenantId), com.facishare.crm.sfa.lto.utils.constants.RiskBrainConstants.KEY, JSONObject.toJSONString(arg), ConfigValueType.JSON);
        } catch (Exception e) {
            log.error("蚂蚁保存账号失败", e);
            throw new ValidateException("蚂蚁保存账号失败");
        }
    }

    public void createAllTask(String tenantId) {
        createTaskForChangeInfoRemind(tenantId);
        createTaskForInfoSync(tenantId);
    }

    public void createTaskForChangeInfoRemind(String tenantId) {
        nomonTask.createOrUpdateTask("risk-brain-change-info-remind", tenantId, serviceFacade.generateId(), new Date(), tenantId);
    }

    public void createTaskForInfoSync(String tenantId) {
        nomonTask.createOrUpdateTask("risk-brain-account-info-sync", tenantId, serviceFacade.generateId(), new Date(), tenantId);
    }

    public void handleBusinessRiskInformationObj(String tenantId) {
        try {
            long time = System.currentTimeMillis() / 1000;
            String body = String.format("{\"refreshAllNet\":false,\"tenantIds\":[\"%s\"],\"funcList\":[{\"func\":\"UpdateRiskInformation\",\"funcName\":\"更新风险信息\"}],\"describeApiNames\":[\"AccountObj\"],\"code\":%s,\"isBrushButton\":true}", tenantId, time);
            crmMetaDataService.addFuncForCRMAdmin(body);
        } catch (Exception e) {
            log.error("handleBusinessRiskInformationObj ", e);
        }
    }

}
